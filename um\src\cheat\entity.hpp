#pragma once
#include <vector>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>

// Project includes
#include "../driver/driver.hpp"
#include "../math/vector.hpp"
#include "bones.hpp"

// create an entity class for our vector, since we need to be able to push_back().
class C_CSPlayerPawn {
 public:
  int health, team;
  int armor; // Added for armor bar
  int entityId; // Added for animation tracking
  std::string PlayerName;
  uint32_t  PlayerFlags;
  uint16_t  ItemDefinitionIndex;
  bool PlayerSpotted;

  Vector Position;

  uint64_t BoneArray;

  uintptr_t pCSPlayerPawn;
};

inline C_CSPlayerPawn CCSPlayerPawn;

class C_BaseEntity {
  public:
    uintptr_t BaseEntity;
    std::string className;
};

inline C_BaseEntity CBaseEntity;

// create a class for filtering players, and our new thread.
class Reader {
public:
  void                        ThreadPlayers();
  std::vector<C_CSPlayerPawn> getPlayerListCopy() const {
    std::lock_guard<std::mutex> lock( playerListMutex );
    return playerList;
  }

  void                        ThreadEntitys();
  std::vector<C_BaseEntity> getEntityListCopy() const {
    std::lock_guard<std::mutex> lock( entityListMutex );
    return entityList;
  }

private:
  void                        FilterPlayers();
  mutable std::mutex          playerListMutex;
  std::vector<C_CSPlayerPawn> playerList;

  void                        FilterEntitys();
  mutable std::mutex          entityListMutex;
  std::vector<C_BaseEntity>   entityList;
};

inline Reader reader;
