#pragma once
#include "../../gamedata.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../../render/render.hpp"
#include <thread>
#include <atomic>
#include <mutex>

class Aimbot
{
public:
  void doAimbot();
  HANDLE driver;
private:
  Vector findClosest(const std::vector<Vector>& playerPositions);
  void MoveMouseToPlayer(Vector position);
  float accumulatedX = 0.0f;
  float accumulatedY = 0.0f;
};

inline Aimbot aimbot;