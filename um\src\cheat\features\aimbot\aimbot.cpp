#include "pch.h"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include "../../../render/render.hpp"

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::doAimbot()
{
  std::vector<Vector> playerPositions;
  playerPositions.clear();

  auto playerList = reader.getPlayerListCopy();
  auto entityList = reader.getEntityListCopy();

  for (const auto& player : playerList)
  {
    Vector playerPosition = driver::read_memory<Vector>(driver, player.BoneArray + bones::head * 32);

    if (player.health <= 0 || player.health > 100)
      continue;



    //if (!player::enemySpotted && player.playerTeam != localTeam && Legitbot.visiblecheck)
     // continue;

    //if (player.playerTeam == localTeam && Legitbot.teamcheck)
      //continue;

    Vector h;
    view_matrix_t viewMatrix = GameData::getViewMatrix();
    if (Vector::world_to_screen(viewMatrix, playerPosition, h))
    {
      playerPositions.push_back(h);
    }
  }

  if (globals::Legitbot::Circle::enabled) {
    float centerX = static_cast<float>(globals::Screen::width) / 2.0f;
    float centerY = static_cast<float>(globals::Screen::height) / 2.0f;
    Render::AACircle(centerX + 0.5f, centerY + 0.5f, globals::Legitbot::radius * 5, globals::Legitbot::Circle::Color, 1.f);
  }

  if (GetAsyncKeyState(0x58)) // X on keyboard
  {
    auto closest_player = findClosest(playerPositions);
    if (!closest_player.IsZero())
    {
      MoveMouseToPlayer(closest_player);
    }
  }
}

Vector Aimbot::findClosest(const std::vector<Vector>& playerPositions) {
  if (playerPositions.empty()) return Vector{0,0,0};

  Vector center_of_screen{
    static_cast<float>(GetSystemMetrics(SM_CXSCREEN)) / 2,
    static_cast<float>(GetSystemMetrics(SM_CYSCREEN)) / 2,
    0.0f
  };

  float max_distance_sq = 5 * globals::Legitbot::radius * globals::Legitbot::radius * 5;
  float closest_distance_sq = FLT_MAX;
  Vector closest = Vector{0,0,0};

  for (const auto& pos : playerPositions) {
    float dx = pos.x - center_of_screen.x;
    float dy = pos.y - center_of_screen.y;
    float distance_sq = dx*dx + dy*dy;

    if (distance_sq < closest_distance_sq && distance_sq < max_distance_sq) {
      closest_distance_sq = distance_sq;
      closest = pos;
    }
  }
  return closest;
}

void Aimbot::MoveMouseToPlayer(Vector position) {
  if (position.IsZero())
    return;

  POINT currentMousePos;
  GetCursorPos(&currentMousePos);
  Vector currentPos{
    static_cast<float>(currentMousePos.x),
    static_cast<float>(currentMousePos.y),
    0.0f
  };

  float deltaX = position.x - currentPos.x;
  float deltaY = position.y - currentPos.y;

  // Bei Smoothness 1 sofort auf den Kopf (keine Gl�ttung)
  if (globals::Legitbot::smoothness <= 1.0f) {
    mouse_event(MOUSEEVENTF_MOVE, static_cast<LONG>(std::round(deltaX)),
      static_cast<LONG>(std::round(deltaY)), 0, 0);
    return;
  }

  const float base_smoothness = max(globals::Legitbot::smoothness, 1.0f);
  const float distance = std::sqrt(deltaX * deltaX + deltaY * deltaY);
  const float adaptive_smoothness = base_smoothness * (1.0f + distance / 1000.0f);

  float stepX = deltaX / adaptive_smoothness;
  float stepY = deltaY / adaptive_smoothness;

  accumulatedX += stepX;
  accumulatedY += stepY;

  LONG moveX = static_cast<LONG>(std::round(accumulatedX));
  LONG moveY = static_cast<LONG>(std::round(accumulatedY));

  accumulatedX -= moveX;
  accumulatedY -= moveY;

  const float deadzone = std::clamp(0.2f * adaptive_smoothness, 0.1f, 2.0f);
  if (std::abs(deltaX) < deadzone && std::abs(deltaY) < deadzone) {
    accumulatedX += deltaX;
    accumulatedY += deltaY;
    return;
  }

  mouse_event(MOUSEEVENTF_MOVE, moveX, moveY, 0, 0);
}

void Aimbot::startAimbotThread() {
  if (isRunning.load()) {
    return; // Already running
  }

  shouldStop.store(false);
  aimbotThread = std::thread(&Aimbot::aimbotThreadFunction, this);
  isRunning.store(true);
}

void Aimbot::stopAimbotThread() {
  if (!isRunning.load()) {
    return; // Not running
  }

  shouldStop.store(true);
  if (aimbotThread.joinable()) {
    aimbotThread.join();
  }
  isRunning.store(false);
}

void Aimbot::renderAimbotVisuals() {
  // Render aimbot circle (thread-safe rendering in main thread)
  if (globals::Legitbot::enabled && globals::Legitbot::Circle::enabled) {
    float centerX = static_cast<float>(globals::Screen::width) / 2.0f;
    float centerY = static_cast<float>(globals::Screen::height) / 2.0f;
    Render::AACircle(centerX + 0.5f, centerY + 0.5f, globals::Legitbot::radius * 5, globals::Legitbot::Circle::Color, 1.f);
  }
}

void Aimbot::aimbotThreadFunction() {
  lastUpdate = std::chrono::steady_clock::now();

  while (!shouldStop.load()) {
    auto now = std::chrono::steady_clock::now();
    if (now - lastUpdate >= updateInterval) {
      if (globals::Legitbot::enabled && GameData::isInitialized()) {
        std::lock_guard<std::mutex> lock(aimbotMutex);
        doAimbot();
      }
      lastUpdate = now;
    }

    std::this_thread::sleep_for(std::chrono::microseconds(100)); // Small sleep to prevent 100% CPU usage
  }
}